#!/usr/bin/env python3
"""
Twx 统一数据库管理工具
自动检测、创建、迁移和维护所有数据库类型的统一脚本
"""

import os
import sys
import json
import sqlite3
import time
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import argparse

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from src.config import Config
from src.utils.logger import setup_logger

logger = setup_logger(module_name="database_manager")

# 尝试导入 PostgreSQL 驱动
try:
    import psycopg2
    import psycopg2.extras
    POSTGRESQL_AVAILABLE = True
except ImportError:
    POSTGRESQL_AVAILABLE = False

# 数据库版本和模式定义
CURRENT_SCHEMA_VERSION = 1

# 统一的表结构定义
DATABASE_SCHEMA = {
    "accounts": {
        "columns": {
            "account_id": {"type": "VARCHAR(255)", "constraints": "PRIMARY KEY"},
            "auth_info_1": {"type": "TEXT", "constraints": ""},
            "auth_info_2": {"type": "TEXT", "constraints": ""},
            "password": {"type": "TEXT", "constraints": ""},
            "totp_secret": {"type": "TEXT", "constraints": ""},
            "cookies": {"type": "TEXT", "constraints": ""},
            "proxy": {"type": "TEXT", "constraints": ""},
            "status": {"type": "VARCHAR(50)", "constraints": "DEFAULT 'active'"},
            "proxy_status": {"type": "VARCHAR(50)", "constraints": ""},
            "last_check": {"type": "TIMESTAMP", "constraints": ""},
            "proxy_latency": {"type": "INTEGER", "constraints": ""},
            "created_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"},
            "updated_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"}
        },
        "indexes": [
            "CREATE INDEX IF NOT EXISTS idx_accounts_status ON accounts(status)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_created_at ON accounts(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_proxy_status ON accounts(proxy_status)",
            "CREATE INDEX IF NOT EXISTS idx_accounts_last_check ON accounts(last_check)"
        ]
    },
    "proxies": {
        "columns": {
            "id": {"type": "SERIAL", "constraints": "", "sqlite_type": "INTEGER PRIMARY KEY AUTOINCREMENT"},
            "ip": {"type": "VARCHAR(45)", "constraints": "NOT NULL"},
            "port": {"type": "INTEGER", "constraints": "NOT NULL"},
            "username": {"type": "TEXT", "constraints": ""},
            "password": {"type": "TEXT", "constraints": ""},
            "proxy_url": {"type": "TEXT", "constraints": ""},
            "country_code": {"type": "VARCHAR(10)", "constraints": ""},
            "city_name": {"type": "VARCHAR(100)", "constraints": ""},
            "assigned_to": {"type": "VARCHAR(255)", "constraints": "REFERENCES accounts(account_id)"},
            "assigned_at": {"type": "TIMESTAMP", "constraints": ""},
            "use_count": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "last_used": {"type": "TIMESTAMP", "constraints": ""},
            "status": {"type": "VARCHAR(50)", "constraints": "DEFAULT 'active'"},
            "created_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"}
        },
        "indexes": [
            "CREATE INDEX IF NOT EXISTS idx_proxies_ip_port ON proxies(ip, port)",
            "CREATE INDEX IF NOT EXISTS idx_proxies_assigned_to ON proxies(assigned_to)",
            "CREATE INDEX IF NOT EXISTS idx_proxies_status ON proxies(status)",
            "CREATE INDEX IF NOT EXISTS idx_proxies_country_code ON proxies(country_code)"
        ]
    },
    "proxy_status": {
        "columns": {
            "id": {"type": "SERIAL", "constraints": "", "sqlite_type": "INTEGER PRIMARY KEY AUTOINCREMENT"},
            "account_id": {"type": "VARCHAR(255)", "constraints": "REFERENCES accounts(account_id)"},
            "proxy_url": {"type": "TEXT", "constraints": ""},
            "status": {"type": "VARCHAR(50)", "constraints": ""},
            "response_time": {"type": "INTEGER", "constraints": ""},
            "error_message": {"type": "TEXT", "constraints": ""},
            "last_check": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"}
        },
        "indexes": [
            "CREATE INDEX IF NOT EXISTS idx_proxy_status_account_id ON proxy_status(account_id)",
            "CREATE INDEX IF NOT EXISTS idx_proxy_status_status ON proxy_status(status)",
            "CREATE INDEX IF NOT EXISTS idx_proxy_status_last_check ON proxy_status(last_check)"
        ]
    },
    "schema_versions": {
        "columns": {
            "component": {"type": "VARCHAR(50)", "constraints": "PRIMARY KEY"},
            "version": {"type": "INTEGER", "constraints": "NOT NULL"},
            "applied_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"}
        },
        "indexes": []
    },
    "account_health": {
        "columns": {
            "account_id": {"type": "VARCHAR(255)", "constraints": "PRIMARY KEY REFERENCES accounts(account_id) ON DELETE CASCADE"},
            "status": {"type": "VARCHAR(50)", "constraints": "DEFAULT 'healthy'"},
            "health_score": {"type": "DECIMAL(5,2)", "constraints": "DEFAULT 100.0", "sqlite_type": "REAL"},
            "risk_score": {"type": "DECIMAL(5,2)", "constraints": "DEFAULT 0.0", "sqlite_type": "REAL"},
            "total_requests": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "successful_requests": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "failed_requests": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "requests_in_hour": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "requests_in_day": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "hour_window_start": {"type": "TIMESTAMP", "constraints": ""},
            "day_window_start": {"type": "TIMESTAMP", "constraints": ""},
            "rate_limit_count": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "auth_failure_count": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "network_error_count": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "consecutive_failures": {"type": "INTEGER", "constraints": "DEFAULT 0"},
            "last_success": {"type": "TIMESTAMP", "constraints": ""},
            "last_failure": {"type": "TIMESTAMP", "constraints": ""},
            "last_rate_limit": {"type": "TIMESTAMP", "constraints": ""},
            "last_request_time": {"type": "TIMESTAMP", "constraints": ""},
            "cooldown_until": {"type": "TIMESTAMP", "constraints": ""},
            "created_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"},
            "updated_at": {"type": "TIMESTAMP", "constraints": "DEFAULT CURRENT_TIMESTAMP"}
        },
        "indexes": [
            "CREATE INDEX IF NOT EXISTS idx_account_health_status ON account_health(status)",
            "CREATE INDEX IF NOT EXISTS idx_account_health_score ON account_health(health_score)",
            "CREATE INDEX IF NOT EXISTS idx_account_health_cooldown ON account_health(cooldown_until)",
            "CREATE INDEX IF NOT EXISTS idx_account_health_updated_at ON account_health(updated_at)"
        ]
    }
}


class DatabaseManager:
    """统一数据库管理器"""
    
    def __init__(self, config_path: str = "config.json"):
        self.config = Config(config_path)
        
    def setup_all_databases(self, verify: bool = True) -> bool:
        """设置所有配置的数据库"""
        db_type = self.config.get_database_type()
        logger.info(f"设置数据库类型: {db_type}")
        
        success = True
        
        if db_type == "sqlite":
            success &= self._setup_sqlite()
        elif db_type == "postgresql":
            success &= self._setup_postgresql()
        elif db_type == "json":
            success &= self._setup_json()
        else:
            logger.error(f"不支持的数据库类型: {db_type}")
            return False
        
        if success and verify:
            success &= self._verify_database(db_type)
            
        return success
    
    def migrate_and_upgrade(self) -> bool:
        """自动检测并执行数据库迁移和升级"""
        db_type = self.config.get_database_type()
        logger.info(f"检查数据库迁移需求: {db_type}")
        
        if db_type == "sqlite":
            return self._migrate_sqlite()
        elif db_type == "postgresql":
            return self._migrate_postgresql()
        elif db_type == "json":
            return self._migrate_json()
        else:
            logger.error(f"不支持的数据库类型: {db_type}")
            return False
    
    def _setup_sqlite(self) -> bool:
        """设置 SQLite 数据库"""
        try:
            sqlite_path = self.config.get_sqlite_path()
            sqlite_dir = Path(sqlite_path).parent
            sqlite_dir.mkdir(parents=True, exist_ok=True)
            
            with sqlite3.connect(sqlite_path) as conn:
                # 启用外键检查
                conn.execute("PRAGMA foreign_keys = ON")
                # 创建所有表
                for table_name, table_def in DATABASE_SCHEMA.items():
                    self._create_sqlite_table(conn, table_name, table_def)
                
                # 设置版本信息
                self._set_schema_version(conn, "sqlite", CURRENT_SCHEMA_VERSION)
                
            logger.info(f"✓ SQLite 数据库设置完成: {sqlite_path}")
            return True
            
        except Exception as e:
            logger.error(f"SQLite 设置失败: {e}")
            return False
    
    def _setup_postgresql(self) -> bool:
        """设置 PostgreSQL 数据库"""
        if not POSTGRESQL_AVAILABLE:
            logger.error("psycopg2 不可用")
            return False
            
        try:
            connection_string = self.config.get_postgresql_connection_string()
            
            with psycopg2.connect(connection_string) as conn:
                with conn.cursor() as cursor:
                    # 创建所有表
                    for table_name, table_def in DATABASE_SCHEMA.items():
                        self._create_postgresql_table(cursor, table_name, table_def)
                    
                    # 创建更新触发器
                    self._create_postgresql_triggers(cursor)
                    
                    # 设置版本信息
                    self._set_schema_version_pg(cursor, "postgresql", CURRENT_SCHEMA_VERSION)
                    
                conn.commit()
                
            logger.info("✓ PostgreSQL 数据库设置完成")
            return True
            
        except Exception as e:
            logger.error(f"PostgreSQL 设置失败: {e}")
            return False
    
    def _setup_json(self) -> bool:
        """设置 JSON 存储"""
        try:
            json_path = self.config.get_json_storage_path()
            json_dir = Path(json_path)
            json_dir.mkdir(parents=True, exist_ok=True)
            
            # 初始化 JSON 结构
            accounts_file = json_dir / "accounts.json"
            if not accounts_file.exists():
                initial_data = {
                    "accounts": [],
                    "proxy_status": [],
                    "schema_versions": {
                        "json": CURRENT_SCHEMA_VERSION,
                        "applied_at": int(time.time())
                    }
                }
                with open(accounts_file, 'w', encoding='utf-8') as f:
                    json.dump(initial_data, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✓ JSON 存储设置完成: {json_path}")
            return True
            
        except Exception as e:
            logger.error(f"JSON 设置失败: {e}")
            return False
    
    def _create_sqlite_table(self, conn: sqlite3.Connection, table_name: str, table_def: Dict) -> None:
        """创建 SQLite 表"""
        columns = []
        for col_name, col_def in table_def["columns"].items():
            col_type = col_def.get("sqlite_type", col_def["type"])
            constraints = col_def["constraints"]
            
            if col_type == "SERIAL":
                col_type = "INTEGER PRIMARY KEY AUTOINCREMENT"
                # 清除原有的PRIMARY KEY约束，避免重复
                constraints = constraints.replace("PRIMARY KEY", "").strip()
            elif col_type == "TIMESTAMP":
                col_type = "DATETIME"
            elif col_type.startswith("VARCHAR"):
                # SQLite不支持VARCHAR，转换为TEXT
                col_type = "TEXT"
            elif col_type.startswith("DECIMAL"):
                # SQLite不支持DECIMAL，转换为REAL
                col_type = "REAL"
            
            columns.append(f"{col_name} {col_type} {constraints}".strip())
        
        create_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})"
        conn.execute(create_sql)
        
        # 创建索引
        for index_sql in table_def.get("indexes", []):
            conn.execute(index_sql)
    
    def _create_postgresql_table(self, cursor, table_name: str, table_def: Dict) -> None:
        """创建 PostgreSQL 表"""
        columns = []
        for col_name, col_def in table_def["columns"].items():
            col_type = col_def["type"]
            if col_type == "TEXT" and table_name == "accounts" and col_name == "account_id":
                col_type = "VARCHAR(255)"
            elif col_type == "TEXT" and "status" in col_name:
                col_type = "VARCHAR(50)"
                
            columns.append(f"{col_name} {col_type} {col_def['constraints']}".strip())
        
        create_sql = f"CREATE TABLE IF NOT EXISTS {table_name} ({', '.join(columns)})"
        cursor.execute(create_sql)
        
        # 创建索引
        for index_sql in table_def.get("indexes", []):
            cursor.execute(index_sql)
    
    def _create_postgresql_triggers(self, cursor) -> None:
        """创建 PostgreSQL 触发器"""
        trigger_function = """
        CREATE OR REPLACE FUNCTION update_updated_at_column()
        RETURNS TRIGGER AS $$
        BEGIN
            NEW.updated_at = CURRENT_TIMESTAMP;
            RETURN NEW;
        END;
        $$ language 'plpgsql';
        """
        cursor.execute(trigger_function)
        
        # 为 accounts 表创建触发器
        trigger_sql = """
        DROP TRIGGER IF EXISTS update_accounts_updated_at ON accounts;
        CREATE TRIGGER update_accounts_updated_at
            BEFORE UPDATE ON accounts
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
        """
        cursor.execute(trigger_sql)
    
    def _migrate_sqlite(self) -> bool:
        """SQLite 数据库迁移"""
        try:
            sqlite_path = self.config.get_sqlite_path()
            if not Path(sqlite_path).exists():
                logger.info("SQLite 数据库不存在，跳过迁移")
                return True
            
            with sqlite3.connect(sqlite_path) as conn:
                # 启用外键检查
                conn.execute("PRAGMA foreign_keys = ON")
                current_version = self._get_schema_version(conn, "sqlite")
                
                if current_version < CURRENT_SCHEMA_VERSION:
                    logger.info(f"执行 SQLite 迁移: {current_version} -> {CURRENT_SCHEMA_VERSION}")
                    
                    # 检查并添加缺失的列
                    self._add_missing_columns_sqlite(conn)
                    
                    # 更新版本
                    self._set_schema_version(conn, "sqlite", CURRENT_SCHEMA_VERSION)
                    logger.info("✓ SQLite 迁移完成")
                else:
                    logger.info("SQLite 数据库已是最新版本")
                    
            return True
            
        except Exception as e:
            logger.error(f"SQLite 迁移失败: {e}")
            return False
    
    def _migrate_postgresql(self) -> bool:
        """PostgreSQL 数据库迁移"""
        if not POSTGRESQL_AVAILABLE:
            return False
            
        try:
            connection_string = self.config.get_postgresql_connection_string()
            
            with psycopg2.connect(connection_string) as conn:
                with conn.cursor() as cursor:
                    current_version = self._get_schema_version_pg(cursor, "postgresql")
                    
                    if current_version < CURRENT_SCHEMA_VERSION:
                        logger.info(f"执行 PostgreSQL 迁移: {current_version} -> {CURRENT_SCHEMA_VERSION}")
                        
                        # 检查并添加缺失的列
                        self._add_missing_columns_postgresql(cursor)
                        
                        # 更新版本
                        self._set_schema_version_pg(cursor, "postgresql", CURRENT_SCHEMA_VERSION)
                        logger.info("✓ PostgreSQL 迁移完成")
                    else:
                        logger.info("PostgreSQL 数据库已是最新版本")
                        
                conn.commit()
            return True
            
        except Exception as e:
            logger.error(f"PostgreSQL 迁移失败: {e}")
            return False
    
    def _migrate_json(self) -> bool:
        """JSON 存储迁移"""
        try:
            json_path = self.config.get_json_storage_path()
            accounts_file = Path(json_path) / "accounts.json"
            
            if not accounts_file.exists():
                logger.info("JSON 文件不存在，跳过迁移")
                return True
            
            with open(accounts_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            current_version = data.get("schema_versions", {}).get("json", 0)
            
            if current_version < CURRENT_SCHEMA_VERSION:
                logger.info(f"执行 JSON 迁移: {current_version} -> {CURRENT_SCHEMA_VERSION}")
                
                # 确保必要的字段存在
                if "proxy_status" not in data:
                    data["proxy_status"] = []
                
                # 更新版本
                data["schema_versions"] = {
                    "json": CURRENT_SCHEMA_VERSION,
                    "applied_at": int(time.time())
                }
                
                with open(accounts_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
                    
                logger.info("✓ JSON 迁移完成")
            else:
                logger.info("JSON 存储已是最新版本")
                
            return True
            
        except Exception as e:
            logger.error(f"JSON 迁移失败: {e}")
            return False
    
    def _add_missing_columns_sqlite(self, conn: sqlite3.Connection) -> None:
        """添加 SQLite 缺失的列"""
        for table_name, table_def in DATABASE_SCHEMA.items():
            
            cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
            table_exists = cursor.fetchone()
            
            if not table_exists:
                # 表不存在，创建它
                self._create_sqlite_table(conn, table_name, table_def)
                continue
            
            # 特殊处理proxies表：如果结构差异太大，重新创建
            if table_name == "proxies":
                self._recreate_proxies_table_if_needed(conn, table_def)
                continue
            
            # 获取现有列
            cursor = conn.execute(f"PRAGMA table_info({table_name})")
            existing_columns = {row[1] for row in cursor.fetchall()}
            
            # 添加缺失的列
            for col_name, col_def in table_def["columns"].items():
                if col_name not in existing_columns:
                    col_type = col_def.get("sqlite_type", col_def["type"])
                    if col_type == "SERIAL":
                        continue  # 不能后添加 AUTOINCREMENT 列
                    elif col_type == "TIMESTAMP":
                        col_type = "DATETIME"
                    elif col_type.startswith("VARCHAR"):
                        # SQLite不支持VARCHAR，转换为TEXT
                        col_type = "TEXT"
                    elif col_type.startswith("DECIMAL"):
                        # SQLite不支持DECIMAL，转换为REAL
                        col_type = "REAL"
                    
                    # 处理NOT NULL约束 - SQLite不允许添加NOT NULL列除非有默认值
                    constraints = col_def['constraints']
                    if "NOT NULL" in constraints and "DEFAULT" not in constraints:
                        # 跳过NOT NULL字段，因为SQLite限制
                        logger.warning(f"跳过添加NOT NULL列: {table_name}.{col_name}")
                        continue
                    
                    alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_type} {constraints}"
                    try:
                        conn.execute(alter_sql)
                        logger.info(f"添加列: {table_name}.{col_name}")
                    except sqlite3.Error as e:
                        logger.warning(f"添加列失败: {table_name}.{col_name}: {e}")

    def _recreate_proxies_table_if_needed(self, conn: sqlite3.Connection, table_def: Dict) -> None:
        """如果proxies表结构差异太大，重新创建表"""
        try:
            # 检查现有proxies表结构
            cursor = conn.execute("PRAGMA table_info(proxies)")
            existing_columns = {row[1]: row[2] for row in cursor.fetchall()}
            
            # 检查是否有id主键
            has_id_pk = 'id' in existing_columns
            has_port = 'port' in existing_columns
            
            if not has_id_pk or not has_port:
                logger.info("proxies表结构需要重新创建")
                
                # 备份现有数据
                cursor = conn.execute("SELECT * FROM proxies")
                existing_data = cursor.fetchall()
                existing_column_names = [description[0] for description in cursor.description]
                
                # 删除旧表
                conn.execute("DROP TABLE proxies")
                
                # 创建新表
                self._create_sqlite_table(conn, "proxies", table_def)
                
                # 尝试迁移数据
                if existing_data:
                    self._migrate_proxies_data(conn, existing_data, existing_column_names)
                    
                logger.info("proxies表重新创建完成")
            else:
                logger.info("proxies表结构正常，跳过重新创建")
                
        except Exception as e:
            logger.error(f"重新创建proxies表失败: {e}")

    def _migrate_proxies_data(self, conn: sqlite3.Connection, old_data: List, old_columns: List) -> None:
        """迁移proxies表的现有数据到新表结构"""
        try:
            # 创建列名映射
            column_mapping = {}
            for i, col_name in enumerate(old_columns):
                column_mapping[col_name] = i
            
            migrated_count = 0
            for row in old_data:
                try:
                    # 提取可迁移的数据
                    ip = row[column_mapping.get('ip', 0)] if 'ip' in column_mapping else None
                    if not ip:
                        continue  # 跳过没有IP的记录
                    
                    # 尝试从IP中提取端口信息
                    port = None
                    if ':' in ip:
                        ip_parts = ip.split(':')
                        if len(ip_parts) == 2 and ip_parts[1].isdigit():
                            ip = ip_parts[0]
                            port = int(ip_parts[1])
                    
                    if not port:
                        port = 8080  # 默认端口
                    
                    # 构建插入数据
                    insert_data = {
                        'ip': ip,
                        'port': port,
                        'username': row[column_mapping.get('username')] if 'username' in column_mapping else None,
                        'password': row[column_mapping.get('password')] if 'password' in column_mapping else None,
                        'proxy_url': row[column_mapping.get('proxy_url')] if 'proxy_url' in column_mapping else None,
                        'country_code': row[column_mapping.get('country_code')] if 'country_code' in column_mapping else None,
                        'city_name': row[column_mapping.get('city_name')] if 'city_name' in column_mapping else None,
                        'assigned_to': row[column_mapping.get('assigned_to')] if 'assigned_to' in column_mapping else None,
                        'assigned_at': row[column_mapping.get('assigned_at')] if 'assigned_at' in column_mapping else None,
                        'use_count': row[column_mapping.get('use_count', 0)] if 'use_count' in column_mapping else 0,
                        'last_used': row[column_mapping.get('last_used')] if 'last_used' in column_mapping else None,
                        'status': row[column_mapping.get('status')] if 'status' in column_mapping else 'active'
                    }
                    
                    # 插入数据
                    conn.execute("""
                        INSERT INTO proxies (ip, port, username, password, proxy_url, 
                                           country_code, city_name, assigned_to, assigned_at,
                                           use_count, last_used, status)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    """, (
                        insert_data['ip'], insert_data['port'], insert_data['username'],
                        insert_data['password'], insert_data['proxy_url'], insert_data['country_code'],
                        insert_data['city_name'], insert_data['assigned_to'], insert_data['assigned_at'],
                        insert_data['use_count'], insert_data['last_used'], insert_data['status']
                    ))
                    
                    migrated_count += 1
                    
                except Exception as e:
                    logger.warning(f"迁移单条proxy记录失败: {e}")
                    
            logger.info(f"成功迁移 {migrated_count} 条proxy记录")
            
        except Exception as e:
            logger.error(f"迁移proxies数据失败: {e}")
    
    def _add_missing_columns_postgresql(self, cursor) -> None:
        """添加 PostgreSQL 缺失的列"""
        for table_name, table_def in DATABASE_SCHEMA.items():
            if table_name == "schema_versions":
                continue
                
            # 检查表是否存在
            cursor.execute("""
                SELECT table_name FROM information_schema.tables 
                WHERE table_schema = 'public' AND table_name = %s
            """, (table_name,))
            
            if not cursor.fetchone():
                # 表不存在，创建它
                self._create_postgresql_table(cursor, table_name, table_def)
                continue
            
            # 获取现有列
            cursor.execute("""
                SELECT column_name FROM information_schema.columns 
                WHERE table_name = %s
            """, (table_name,))
            existing_columns = {row[0] for row in cursor.fetchall()}
            
            # 添加缺失的列
            for col_name, col_def in table_def["columns"].items():
                if col_name not in existing_columns:
                    col_type = col_def["type"]
                    if col_type == "SERIAL":
                        continue  # 不能后添加 SERIAL 列
                    elif col_type == "TEXT" and "status" in col_name:
                        col_type = "VARCHAR(50)"
                    elif col_type == "TEXT" and table_name == "accounts" and col_name == "account_id":
                        col_type = "VARCHAR(255)"
                    
                    alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {col_name} {col_type} {col_def['constraints']}"
                    try:
                        cursor.execute(alter_sql)
                        logger.info(f"添加列: {table_name}.{col_name}")
                    except Exception as e:
                        logger.warning(f"添加列失败: {table_name}.{col_name}: {e}")
    
    def _get_schema_version(self, conn: sqlite3.Connection, component: str) -> int:
        """获取 SQLite 模式版本"""
        try:
            cursor = conn.execute("SELECT version FROM schema_versions WHERE component = ?", (component,))
            result = cursor.fetchone()
            return result[0] if result else 0
        except sqlite3.Error:
            return 0
    
    def _get_schema_version_pg(self, cursor, component: str) -> int:
        """获取 PostgreSQL 模式版本"""
        try:
            cursor.execute("SELECT version FROM schema_versions WHERE component = %s", (component,))
            result = cursor.fetchone()
            return result[0] if result else 0
        except Exception:
            return 0
    
    def _set_schema_version(self, conn: sqlite3.Connection, component: str, version: int) -> None:
        """设置 SQLite 模式版本"""
        conn.execute("""
            INSERT OR REPLACE INTO schema_versions (component, version, applied_at) 
            VALUES (?, ?, CURRENT_TIMESTAMP)
        """, (component, version))
    
    def _set_schema_version_pg(self, cursor, component: str, version: int) -> None:
        """设置 PostgreSQL 模式版本"""
        cursor.execute("""
            INSERT INTO schema_versions (component, version, applied_at) 
            VALUES (%s, %s, CURRENT_TIMESTAMP)
            ON CONFLICT (component) DO UPDATE SET 
                version = EXCLUDED.version,
                applied_at = CURRENT_TIMESTAMP
        """, (component, version))
    
    def _verify_database(self, db_type: str) -> bool:
        """验证数据库设置"""
        try:
            if db_type == "sqlite":
                return self._verify_sqlite()
            elif db_type == "postgresql":
                return self._verify_postgresql()
            elif db_type == "json":
                return self._verify_json()
            return False
        except Exception as e:
            logger.error(f"数据库验证失败: {e}")
            return False
    
    def _verify_sqlite(self) -> bool:
        """验证 SQLite 数据库"""
        sqlite_path = self.config.get_sqlite_path()
        if not Path(sqlite_path).exists():
            logger.error("SQLite 数据库文件不存在")
            return False
        
        with sqlite3.connect(sqlite_path) as conn:
            conn.execute("PRAGMA foreign_keys = ON")
            for table_name in DATABASE_SCHEMA.keys():
                cursor = conn.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
                if not cursor.fetchone():
                    logger.error(f"表不存在: {table_name}")
                    return False
        
        logger.info("✓ SQLite 数据库验证通过")
        return True
    
    def _verify_postgresql(self) -> bool:
        """验证 PostgreSQL 数据库"""
        if not POSTGRESQL_AVAILABLE:
            return False
        
        connection_string = self.config.get_postgresql_connection_string()
        with psycopg2.connect(connection_string) as conn:
            with conn.cursor() as cursor:
                for table_name in DATABASE_SCHEMA.keys():
                    cursor.execute("""
                        SELECT table_name FROM information_schema.tables 
                        WHERE table_schema = 'public' AND table_name = %s
                    """, (table_name,))
                    if not cursor.fetchone():
                        logger.error(f"表不存在: {table_name}")
                        return False
        
        logger.info("✓ PostgreSQL 数据库验证通过")
        return True
    
    def _verify_json(self) -> bool:
        """验证 JSON 存储"""
        json_path = self.config.get_json_storage_path()
        accounts_file = Path(json_path) / "accounts.json"
        
        if not accounts_file.exists():
            logger.error("JSON 文件不存在")
            return False
        
        with open(accounts_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            if not isinstance(data, dict) or "accounts" not in data:
                logger.error("JSON 文件格式错误")
                return False
        
        logger.info("✓ JSON 存储验证通过")
        return True

    def migrate_sqlite_to_postgresql(self, backup: bool = True) -> bool:
        """从 SQLite 迁移到 PostgreSQL"""
        if not POSTGRESQL_AVAILABLE:
            logger.error("PostgreSQL 不可用")
            return False
        
        try:
            sqlite_path = self.config.get_sqlite_path()
            if not Path(sqlite_path).exists():
                logger.error("SQLite 数据库不存在")
                return False
            
            # 读取 SQLite 数据
            accounts = []
            with sqlite3.connect(sqlite_path) as conn:
                conn.execute("PRAGMA foreign_keys = ON")
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM accounts")
                accounts = [dict(row) for row in cursor.fetchall()]
            
            if not accounts:
                logger.info("没有数据需要迁移")
                return True
            
            logger.info(f"准备迁移 {len(accounts)} 个账户")
            
            # 备份现有 PostgreSQL 数据
            if backup:
                self._backup_postgresql_data()
            
            # 写入 PostgreSQL
            connection_string = self.config.get_postgresql_connection_string()
            with psycopg2.connect(connection_string) as conn:
                with conn.cursor() as cursor:
                    for account in accounts:
                        cursor.execute("""
                            INSERT INTO accounts (
                                account_id, auth_info_1, auth_info_2, password,
                                totp_secret, cookies, proxy, status,
                                proxy_status, last_check, proxy_latency,
                                created_at, updated_at
                            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                            ON CONFLICT (account_id) DO UPDATE SET
                                auth_info_1 = EXCLUDED.auth_info_1,
                                auth_info_2 = EXCLUDED.auth_info_2,
                                password = EXCLUDED.password,
                                totp_secret = EXCLUDED.totp_secret,
                                cookies = EXCLUDED.cookies,
                                proxy = EXCLUDED.proxy,
                                status = EXCLUDED.status,
                                proxy_status = EXCLUDED.proxy_status,
                                last_check = EXCLUDED.last_check,
                                proxy_latency = EXCLUDED.proxy_latency,
                                updated_at = CURRENT_TIMESTAMP
                        """, tuple(account.get(key) for key in [
                            'account_id', 'auth_info_1', 'auth_info_2', 'password',
                            'totp_secret', 'cookies', 'proxy', 'status',
                            'proxy_status', 'last_check', 'proxy_latency',
                            'created_at', 'updated_at'
                        ]))
                    
                conn.commit()
            
            logger.info("✓ 数据迁移完成")
            return True
            
        except Exception as e:
            logger.error(f"迁移失败: {e}")
            return False
    
    def _backup_postgresql_data(self) -> None:
        """备份 PostgreSQL 数据"""
        try:
            backup_file = Path(f"backup_postgresql_{int(time.time())}.json")
            connection_string = self.config.get_postgresql_connection_string()
            
            with psycopg2.connect(connection_string) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cursor:
                    cursor.execute("SELECT * FROM accounts")
                    existing_data = [dict(row) for row in cursor.fetchall()]
            
            if existing_data:
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(existing_data, f, indent=2, default=str, ensure_ascii=False)
                logger.info(f"PostgreSQL 数据已备份: {backup_file}")
                
        except Exception as e:
            logger.warning(f"备份失败: {e}")


def main():
    parser = argparse.ArgumentParser(description="Twx 统一数据库管理工具")
    parser.add_argument("command", choices=[
        "setup", "migrate", "verify", "migrate-to-pg", "status"
    ], help="要执行的操作")
    parser.add_argument("--config", help="配置文件路径", default="config.json")
    parser.add_argument("--no-verify", action="store_true", help="跳过验证")
    parser.add_argument("--no-backup", action="store_true", help="跳过备份")
    
    args = parser.parse_args()
    
    try:
        manager = DatabaseManager(args.config)
        
        if args.command == "setup":
            success = manager.setup_all_databases(verify=not args.no_verify)
            logger.info("✅ 数据库设置完成" if success else "❌ 数据库设置失败")
            
        elif args.command == "migrate":
            success = manager.migrate_and_upgrade()
            logger.info("✅ 数据库迁移完成" if success else "❌ 数据库迁移失败")
            
        elif args.command == "verify":
            db_type = manager.config.get_database_type()
            success = manager._verify_database(db_type)
            logger.info("✅ 数据库验证通过" if success else "❌ 数据库验证失败")
            
        elif args.command == "migrate-to-pg":
            success = manager.migrate_sqlite_to_postgresql(backup=not args.no_backup)
            logger.info("✅ 迁移到 PostgreSQL 完成" if success else "❌ 迁移失败")
            
        elif args.command == "status":
            db_type = manager.config.get_database_type()
            logger.info(f"当前数据库类型: {db_type}")
            success = manager._verify_database(db_type)
        
        return 0 if success else 1
        
    except Exception as e:
        logger.error(f"操作失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())